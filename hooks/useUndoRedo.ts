import { useState, useCallback, useRef } from 'react';

interface UndoRedoState<T> {
  past: T[];
  present: T;
  future: T[];
}

interface UndoRedoActions {
  canUndo: boolean;
  canRedo: boolean;
  undo: () => void;
  redo: () => void;
  set: (newState: any) => void;
  reset: (newState: any) => void;
  clear: () => void;
}

export function useUndoRedo<T>(initialState: T, maxHistorySize: number = 50): [T, UndoRedoActions] {
  const [state, setState] = useState<UndoRedoState<T>>({
    past: [],
    present: initialState,
    future: [],
  });

  const timeoutRef = useRef<NodeJS.Timeout>();

  const canUndo = state.past.length > 0;
  const canRedo = state.future.length > 0;

  const undo = useCallback(() => {
    setState((currentState) => {
      if (currentState.past.length === 0) return currentState;

      const previous = currentState.past[currentState.past.length - 1];
      const newPast = currentState.past.slice(0, currentState.past.length - 1);

      return {
        past: newPast,
        present: previous,
        future: [currentState.present, ...currentState.future],
      };
    });
  }, []);

  const redo = useCallback(() => {
    setState((currentState) => {
      if (currentState.future.length === 0) return currentState;

      const next = currentState.future[0];
      const newFuture = currentState.future.slice(1);

      return {
        past: [...currentState.past, currentState.present],
        present: next,
        future: newFuture,
      };
    });
  }, []);

  const set = useCallback((newState: T) => {
    // Clear any pending timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // Immediately update the present state for responsive UI
    setState((currentState) => ({
      ...currentState,
      present: newState,
    }));

    // Debounce history updates to avoid creating too many history entries
    timeoutRef.current = setTimeout(() => {
      setState((currentState) => {
        // Don't add to history if the state hasn't actually changed
        if (JSON.stringify(currentState.past[currentState.past.length - 1] || {}) === JSON.stringify(newState)) {
          return currentState;
        }

        const newPast = [...currentState.past, currentState.present];

        // Limit history size
        if (newPast.length > maxHistorySize) {
          newPast.shift();
        }

        return {
          past: newPast,
          present: newState,
          future: [], // Clear future when new state is set
        };
      });
    }, 100); // Reduced to 100ms for better responsiveness
  }, [maxHistorySize]);

  const reset = useCallback((newState: T) => {
    // Clear any pending timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    setState({
      past: [],
      present: newState,
      future: [],
    });
  }, []);

  const clear = useCallback(() => {
    // Clear any pending timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    setState((currentState) => ({
      past: [],
      present: currentState.present,
      future: [],
    }));
  }, []);

  return [
    state.present,
    {
      canUndo,
      canRedo,
      undo,
      redo,
      set,
      reset,
      clear,
    },
  ];
}

export default useUndoRedo;
